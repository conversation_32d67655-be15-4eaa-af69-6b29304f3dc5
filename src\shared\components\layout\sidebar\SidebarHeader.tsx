import { Sparkles } from "lucide-react";

type SidebarHeaderProps = {
  isIconOnly: boolean;
};

export function SidebarHeader({ isIconOnly }: SidebarHeaderProps) {
  return (
    <div className="relative p-6 border-b border-slate-200 dark:border-gray-600 transition-all duration-300 h-20 flex items-center bg-gradient-to-r from-slate-50 to-blue-50 dark:from-gray-800 dark:to-gray-900">
      {/* Decorative background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-500 opacity-5 dark:opacity-10" />
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500" />

      <div className="relative flex items-center w-full">
        {/* Logo container with modern styling */}
        <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105">
          <div className="flex items-center justify-center w-full h-full rounded-xl bg-white bg-opacity-10">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
        </div>

        {!isIconOnly && (
          <div className="ml-4 transition-all duration-300 opacity-100">
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-bold text-slate-800 dark:text-gray-100">
                JARSS
              </h1>
              <div className="px-2 py-0.5 text-xs font-medium text-blue-600 bg-blue-100 dark:text-blue-300 dark:bg-blue-900/50 rounded-full">
                Pro
              </div>
            </div>
            <p className="text-sm text-slate-500 dark:text-gray-300 font-medium">
              Sistema Contable
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
