import { Menu, X } from "lucide-react";
import { Button } from "@/shared/components/ui/button";

type SidebarToggleProps = {
  isOpen: boolean;
  toggleSidebar: () => void;
};

export function SidebarToggle({ isOpen, toggleSidebar }: SidebarToggleProps) {
  return (
    <Button
      variant="outline"
      size="icon"
      className="fixed top-6 left-6 z-50 lg:hidden rounded-xl bg-white/95 backdrop-blur-xl shadow-xl border border-slate-200/50 dark:bg-gray-900/95 dark:border-gray-600/50 hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105 hover:shadow-2xl"
      onClick={toggleSidebar}
    >
      <div className="relative">
        {isOpen ? (
          <X size={20} className="text-slate-700 dark:text-gray-200 transition-transform duration-300 rotate-0" />
        ) : (
          <Menu size={20} className="text-slate-700 dark:text-gray-200 transition-transform duration-300 rotate-0" />
        )}
        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300 -z-10" />
      </div>
    </Button>
  );
}
