import { Link } from "react-router-dom";
import { ChevronDown, ChevronRight, Circle } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/shared/components/ui/tooltip";

type SubmenuItem = {
  name: string;
  href: string;
};

type SidebarSubmenuProps = {
  name: string;
  icon: React.ComponentType<any>;
  href: string;
  submenu: SubmenuItem[];
  isOpen: boolean;
  isIconOnly: boolean;
  currentPath: string;
  onToggle: (name: string) => void;
};

export function SidebarSubmenu({
  name,
  icon: Icon,
  href,
  submenu,
  isOpen,
  isIconOnly,
  currentPath,
  onToggle
}: SidebarSubmenuProps) {
  const handleToggle = () => {
    onToggle(name);
  };

  const hasActiveSubmenu = submenu.some(item => currentPath === item.href);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={handleToggle}
              className={cn(
                "relative flex items-center justify-between w-full px-3 py-3 rounded-xl group transition-all duration-300 overflow-hidden",
                isOpen || hasActiveSubmenu
                  ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25 scale-[1.02]"
                  : "text-slate-700 dark:text-gray-200 hover:bg-slate-100 dark:hover:bg-gray-700 hover:text-slate-900 dark:hover:text-white",
                isIconOnly ? "justify-center mx-2" : "mx-2"
              )}
            >
              {/* Active indicator */}
              {(isOpen || hasActiveSubmenu) && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 animate-pulse" />
              )}

              <div className="relative flex items-center">
                {/* Icon container */}
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-300",
                  isOpen || hasActiveSubmenu
                    ? "bg-white/20 backdrop-blur-sm"
                    : "group-hover:bg-slate-200 dark:group-hover:bg-gray-600"
                )}>
                  <Icon className={cn(
                    "h-5 w-5 transition-all duration-300",
                    isOpen || hasActiveSubmenu
                      ? "text-white"
                      : "text-slate-600 dark:text-gray-300 group-hover:text-slate-800 dark:group-hover:text-white",
                    "group-hover:scale-110"
                  )} />
                </div>

                {!isIconOnly && (
                  <span className={cn(
                    "ml-3 text-sm font-medium transition-all duration-300",
                    isOpen || hasActiveSubmenu ? "text-white" : "group-hover:translate-x-1"
                  )}>
                    {name}
                  </span>
                )}
              </div>

              {!isIconOnly && (
                <div className="relative">
                  <ChevronRight className={cn(
                    "w-4 h-4 transition-transform duration-300",
                    isOpen ? "rotate-90" : "",
                    isOpen || hasActiveSubmenu ? "text-white" : "text-slate-400 dark:text-gray-400"
                  )} />
                </div>
              )}

              {/* Hover effect overlay */}
              {!(isOpen || hasActiveSubmenu) && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              )}
            </button>
          </TooltipTrigger>
          {isIconOnly && (
            <TooltipContent
              side="right"
              className="bg-gray-900 text-white border-gray-700 shadow-xl dark:bg-gray-800 dark:border-gray-600"
            >
              <div className="font-medium">{name}</div>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>

      {isOpen && !isIconOnly && (
        <div className="mt-2 ml-2 mr-2 space-y-1 animate-in slide-in-from-top-2 duration-200">
          <div className="relative pl-6 space-y-1">
            {/* Connection line */}
            <div className="absolute left-6 top-0 bottom-0 w-px bg-gradient-to-b from-slate-300 to-transparent dark:from-gray-500" />

            {submenu.map((subitem, index) => (
              <SubmenuItem
                key={subitem.name}
                name={subitem.name}
                href={subitem.href}
                isActive={currentPath === subitem.href}
                isLast={index === submenu.length - 1}
              />
            ))}
          </div>
        </div>
      )}
    </>
  );
}

type SubmenuItemProps = {
  name: string;
  href: string;
  isActive: boolean;
  isLast: boolean;
};

function SubmenuItem({ name, href, isActive, isLast }: SubmenuItemProps) {
  return (
    <div className="relative">
      {/* Connection dot */}
      <div className="absolute left-[-1.5rem] top-3 w-2 h-2 rounded-full bg-slate-300 dark:bg-gray-500 transition-colors duration-300" />

      <Link
        to={href}
        className={cn(
          "relative flex items-center px-3 py-2.5 text-sm rounded-lg transition-all duration-300 group",
          isActive
            ? "text-blue-600 dark:text-blue-300 font-medium bg-blue-50 dark:bg-blue-900/30 shadow-sm"
            : "text-slate-600 dark:text-gray-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-gray-700/50"
        )}
      >
        {/* Active indicator */}
        {isActive && (
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-gradient-to-b from-blue-500 to-indigo-500 rounded-r-full" />
        )}

        <div className="flex items-center gap-3">
          <Circle className={cn(
            "w-1.5 h-1.5 transition-all duration-300",
            isActive
              ? "text-blue-500 fill-current"
              : "text-slate-400 dark:text-gray-400 group-hover:text-slate-600 dark:group-hover:text-gray-200"
          )} />
          <span className={cn(
            "transition-all duration-300",
            isActive ? "font-medium" : "group-hover:translate-x-1"
          )}>
            {name}
          </span>
        </div>

        {/* Hover effect */}
        {!isActive && (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
        )}
      </Link>
    </div>
  );
}
