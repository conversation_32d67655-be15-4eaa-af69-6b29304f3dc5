import { api } from '@/infrastructure/api';

interface FinancialSummary {
  totalIncome: number;
  totalExpenses: number;
  balance: number;
  pendingPayments: number;
}

class FinancialService {
  async getFinancialSummary(): Promise<FinancialSummary> {
    const response = await api.get('/financial/summary');
    return response.data;
  }

  async getTransactions(params: {
    startDate: string;
    endDate: string;
    type?: 'income' | 'expense';
  }) {
    const response = await api.get('/financial/transactions', { params });
    return response.data;
  }

  async createTransaction(data: {
    amount: number;
    type: 'income' | 'expense';
    description: string;
    date: string;
    category: string;
  }) {
    const response = await api.post('/financial/transactions', data);
    return response.data;
  }
}

export const financialService = new FinancialService(); 