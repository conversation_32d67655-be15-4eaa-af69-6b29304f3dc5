import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { useFinancialData } from '../hooks/useFinancialData';

export const FinancialDashboard: React.FC = () => {
  const { data, isLoading, error } = useFinancialData();

  if (isLoading) return <div>Cargando...</div>;
  if (error) return <div>Error al cargar los datos financieros</div>;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ingresos Totales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${data?.totalIncome || 0}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Gastos Totales</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${data?.totalExpenses || 0}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Balance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${data?.balance || 0}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pendiente por Cobrar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${data?.pendingPayments || 0}</div>
        </CardContent>
      </Card>
    </div>
  );
}; 