@echo off
REM JARSS - Configuración con Docker para Windows
REM Sistema Integral de Gestión Financiera

echo.
echo   ╔══════════════════════════════════════════════════════════════╗
echo   ║                            JARSS                             ║
echo   ║              Sistema Integral de Gestión Financiera          ║
echo   ║                   Configuración con Docker                   ║
echo   ╚══════════════════════════════════════════════════════════════╝
echo.

REM Verificar si Docker está instalado
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker no está instalado. Por favor instala Docker Desktop primero.
    echo    Descarga desde: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker encontrado

REM Verificar si Docker está corriendo
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker no está corriendo. Por favor inicia Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker está corriendo

REM Variables de configuración
set DB_NAME=jarss
set DB_USER=jarss_user
set DB_PASSWORD=jarss_password_2025
set DB_PORT=5432
set CONTAINER_NAME=jarss-postgres

echo.
echo 📊 Configuración de la base de datos:
echo   • Nombre: %DB_NAME%
echo   • Usuario: %DB_USER%
echo   • Puerto: %DB_PORT%
echo   • Contenedor: %CONTAINER_NAME%
echo.

REM Detener y eliminar contenedor existente si existe
echo 🔄 Limpiando instalación anterior...
docker stop %CONTAINER_NAME% >nul 2>&1
docker rm %CONTAINER_NAME% >nul 2>&1

REM Crear y ejecutar contenedor PostgreSQL
echo 🚀 Creando contenedor PostgreSQL...
docker run -d ^
  --name %CONTAINER_NAME% ^
  -e POSTGRES_DB=%DB_NAME% ^
  -e POSTGRES_USER=%DB_USER% ^
  -e POSTGRES_PASSWORD=%DB_PASSWORD% ^
  -p %DB_PORT%:5432 ^
  -v jarss-data:/var/lib/postgresql/data ^
  postgres:15

if %errorlevel% neq 0 (
    echo ❌ Error al crear el contenedor PostgreSQL
    pause
    exit /b 1
)

echo ✅ Contenedor PostgreSQL creado

REM Esperar a que PostgreSQL esté listo
echo ⏳ Esperando a que PostgreSQL esté listo...
timeout /t 10 /nobreak >nul

REM Verificar conexión
:check_connection
docker exec %CONTAINER_NAME% pg_isready -U %DB_USER% -d %DB_NAME% >nul 2>&1
if %errorlevel% neq 0 (
    echo    Esperando conexión...
    timeout /t 2 /nobreak >nul
    goto check_connection
)

echo ✅ PostgreSQL está listo

REM Instalar JARSS
echo 📦 Instalando esquema de JARSS...

REM Copiar archivos SQL al contenedor
docker cp install.sql %CONTAINER_NAME%:/tmp/install.sql
docker cp schema.sql %CONTAINER_NAME%:/tmp/schema.sql
docker cp functions.sql %CONTAINER_NAME%:/tmp/functions.sql
docker cp triggers.sql %CONTAINER_NAME%:/tmp/triggers.sql
docker cp seed_data.sql %CONTAINER_NAME%:/tmp/seed_data.sql

REM Ejecutar instalación
docker exec %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% -f /tmp/install.sql

if %errorlevel% neq 0 (
    echo ❌ Error durante la instalación de JARSS
    pause
    exit /b 1
)

echo ✅ JARSS instalado exitosamente

REM Crear archivo .env
echo 📝 Creando archivo de configuración...
(
echo # JARSS - Sistema Integral de Gestión Financiera
echo # Configuración generada automáticamente con Docker
echo # Fecha: %date% %time%
echo.
echo # Database Configuration ^(PostgreSQL en Docker^)
echo DATABASE_HOST=localhost
echo DATABASE_PORT=%DB_PORT%
echo DATABASE_NAME=%DB_NAME%
echo DATABASE_USER=%DB_USER%
echo DATABASE_PASSWORD=%DB_PASSWORD%
echo DATABASE_SSL=false
echo.
echo # Application Configuration
echo VITE_APP_NAME=JARSS
echo VITE_APP_VERSION=1.0.0
echo VITE_APP_ENVIRONMENT=development
echo.
echo # API Configuration
echo VITE_API_URL=http://localhost:3000/api
echo API_PORT=3000
echo.
echo # Authentication ^& Security
echo JWT_SECRET=jarss_jwt_secret_%random%
echo JWT_EXPIRES_IN=24h
echo SESSION_TIMEOUT=3600
echo MAX_LOGIN_ATTEMPTS=5
echo BCRYPT_ROUNDS=12
echo.
echo # Dominican Republic Tax Configuration
echo VITE_ITBIS_RATE=0.18
echo VITE_AFP_RATE=0.0287
echo VITE_SFS_RATE=0.0304
echo VITE_INFOTEP_RATE=0.01
echo.
echo # Feature Flags
echo VITE_ENABLE_DEMO_DATA=true
echo.
echo # Docker Configuration
echo DOCKER_CONTAINER_NAME=%CONTAINER_NAME%
) > ..\.env

echo ✅ Archivo .env creado

REM Crear datos de demostración
echo.
set /p create_demo="¿Deseas crear datos de demostración? (Y/N): "
if /i "%create_demo%"=="Y" (
    echo 🎭 Creando datos de demostración...
    
    docker exec %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% -c "INSERT INTO users (email, password_hash, full_name) VALUES ('<EMAIL>', hash_password('demo123'), 'Usuario Demo');"
    
    docker exec %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% -c "INSERT INTO companies (owner_id, name, rnc, address) SELECT id, 'Empresa Demo JARSS', '101-12345-6', 'Santo Domingo, República Dominicana' FROM users WHERE email = '<EMAIL>';"
    
    docker exec %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% -c "SELECT setup_new_company(c.id) FROM companies c JOIN users u ON c.owner_id = u.id WHERE u.email = '<EMAIL>';"
    
    docker exec %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME% -c "SELECT create_demo_data(c.id) FROM companies c JOIN users u ON c.owner_id = u.id WHERE u.email = '<EMAIL>';"
    
    echo ✅ Datos de demostración creados
    echo.
    echo 🔑 Credenciales de demostración:
    echo   Email: <EMAIL>
    echo   Contraseña: demo123
)

REM Resumen final
echo.
echo 🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE! 🎉
echo.
echo 📋 RESUMEN DE CONFIGURACIÓN:
echo   • Contenedor Docker: %CONTAINER_NAME%
echo   • Base de datos: %DB_NAME%
echo   • Usuario: %DB_USER%
echo   • Puerto: %DB_PORT%
echo   • Archivo de configuración: .env
echo.
echo 🚀 COMANDOS ÚTILES:
echo   • Conectar a la DB: docker exec -it %CONTAINER_NAME% psql -U %DB_USER% -d %DB_NAME%
echo   • Ver logs: docker logs %CONTAINER_NAME%
echo   • Detener: docker stop %CONTAINER_NAME%
echo   • Iniciar: docker start %CONTAINER_NAME%
echo   • Eliminar: docker stop %CONTAINER_NAME% ^&^& docker rm %CONTAINER_NAME%
echo.
echo 📚 DOCUMENTACIÓN:
echo   • README.md - Información general
echo   • database\README.md - Documentación de la base de datos
echo.
echo ⚠️  IMPORTANTE:
echo   • El contenedor se ejecuta en segundo plano
echo   • Los datos se guardan en el volumen 'jarss-data'
echo   • Para producción, cambia las contraseñas por defecto
echo.
echo ✅ JARSS está listo para usar!
echo.
pause
