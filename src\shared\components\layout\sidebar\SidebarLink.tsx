import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/shared/components/ui/tooltip";

type SidebarLinkProps = {
  name: string;
  icon: React.ComponentType<any>;
  href: string;
  isActive: boolean;
  isIconOnly: boolean;
};

export function SidebarLink({ name, icon: Icon, href, isActive, isIconOnly }: SidebarLinkProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            to={href}
            className={cn(
              "relative flex items-center px-3 py-3 rounded-xl group transition-all duration-300",
              isActive
                ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                : "text-slate-700 dark:text-gray-200 hover:bg-slate-100 dark:hover:bg-gray-700 hover:text-slate-900 dark:hover:text-white",
              isIconOnly ? "justify-center mx-2" : "mx-2"
            )}
          >
            {/* Icon container */}
            <div className={cn(
              "relative flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-300",
              isActive
                ? "bg-white bg-opacity-20"
                : "group-hover:bg-slate-200 dark:group-hover:bg-gray-600"
            )}>
              <Icon className={cn(
                "h-5 w-5 transition-all duration-300",
                isActive
                  ? "text-white"
                  : "text-slate-600 dark:text-gray-300 group-hover:text-slate-800 dark:group-hover:text-white",
                "group-hover:scale-110"
              )} />
            </div>

            {!isIconOnly && (
              <div className="relative ml-3 flex-1">
                <span className={cn(
                  "text-sm font-medium transition-all duration-300",
                  isActive ? "text-white" : "group-hover:translate-x-1"
                )}>
                  {name}
                </span>
                {/* Subtle animation line */}
                <div className={cn(
                  "absolute bottom-0 left-0 h-0.5 bg-current transition-all duration-300",
                  isActive ? "w-full opacity-100" : "w-0 group-hover:w-full opacity-60"
                )} />
              </div>
            )}
          </Link>
        </TooltipTrigger>
        {isIconOnly && (
          <TooltipContent
            side="right"
            className="bg-gray-900 text-white border-gray-700 shadow-xl dark:bg-gray-800 dark:border-gray-600"
          >
            <div className="font-medium">{name}</div>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}
