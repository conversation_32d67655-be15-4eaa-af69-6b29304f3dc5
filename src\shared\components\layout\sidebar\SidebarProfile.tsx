import { cn } from "@/lib/utils";
import { LogOut, Settings, User, ChevronUp } from "lucide-react";
import { useState } from "react";

type SidebarProfileProps = {
  isIconOnly: boolean;
};

export function SidebarProfile({ isIconOnly }: SidebarProfileProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="relative">
      {/* Profile Card */}
      <div
        className={cn(
          "p-4 border-t border-slate-200 dark:border-gray-600 bg-gradient-to-r from-slate-50/50 to-blue-50/30 dark:from-gray-800/50 dark:to-gray-900/20 transition-all duration-300",
          isIconOnly ? "text-center" : "cursor-pointer hover:bg-slate-100/50 dark:hover:bg-gray-700/50"
        )}
        onClick={() => !isIconOnly && setIsMenuOpen(!isMenuOpen)}
      >
        <div className={cn(
          "flex items-center",
          isIconOnly ? "justify-center" : "justify-between"
        )}>
          <div className={cn(
            "flex items-center",
            isIconOnly ? "flex-col gap-2" : "gap-3"
          )}>
            {/* Avatar with modern styling */}
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/30">
                <User className="w-5 h-5" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
            </div>

            {!isIconOnly && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-slate-800 dark:text-gray-100 truncate">
                  Joe Doe
                </p>
                <p className="text-xs text-slate-500 dark:text-gray-300 truncate">
                  <EMAIL>
                </p>
              </div>
            )}
          </div>

          {!isIconOnly && (
            <ChevronUp
              className={cn(
                "w-4 h-4 text-slate-400 dark:text-gray-400 transition-transform duration-200",
                isMenuOpen ? "rotate-180" : ""
              )}
            />
          )}
        </div>
      </div>

      {/* Dropdown Menu */}
      {!isIconOnly && isMenuOpen && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-slate-200 dark:border-gray-600 overflow-hidden animate-in slide-in-from-bottom-2 duration-200">
          <div className="p-2 space-y-1">
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-slate-700 dark:text-gray-200 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
              <User className="w-4 h-4" />
              Mi Perfil
            </button>
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-slate-700 dark:text-gray-200 hover:bg-slate-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
              <Settings className="w-4 h-4" />
              Configuración
            </button>
            <hr className="my-2 border-slate-200 dark:border-gray-600" />
            <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors duration-200">
              <LogOut className="w-4 h-4" />
              Cerrar Sesión
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
