import { useQuery } from '@tanstack/react-query';
import { financialService } from '../services/financialService';

interface FinancialData {
  totalIncome: number;
  totalExpenses: number;
  balance: number;
  pendingPayments: number;
}

export const useFinancialData = () => {
  return useQuery<FinancialData>({
    queryKey: ['financialData'],
    queryFn: () => financialService.getFinancialSummary(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}; 