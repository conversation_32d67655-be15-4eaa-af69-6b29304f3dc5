-- JARSS - Consultas de Demostración
-- Sistema Integral de Gestión Financiera
-- Ejemplos de uso de la base de datos

-- =====================================================
-- CONSULTAS DE DEMOSTRACIÓN PARA JARSS
-- =====================================================

-- Estas consultas muestran las capacidades del sistema
-- Ejecutar después de instalar la base de datos y crear datos demo

-- =====================================================
-- 1. VERIFICAR INSTALACIÓN
-- =====================================================

-- Mostrar todas las tablas creadas
SELECT 
    table_name as "Tabla",
    table_type as "Tipo"
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Contar registros en tablas principales
SELECT 
    'users' as tabla, COUNT(*) as registros FROM users
UNION ALL
SELECT 'companies', COUNT(*) FROM companies
UNION ALL
SELECT 'financial_accounts', COUNT(*) FROM financial_accounts
UNION ALL
SELECT 'financial_transactions', COUNT(*) FROM financial_transactions
UNION ALL
SELECT 'employees', COUNT(*) FROM employees
UNION ALL
SELECT 'transaction_categories', COUNT(*) FROM transaction_categories
UNION ALL
SELECT 'tax_configurations', COUNT(*) FROM tax_configurations
ORDER BY tabla;

-- =====================================================
-- 2. FUNCIONES DOMINICANAS
-- =====================================================

-- Probar cálculo de deducciones dominicanas
SELECT 
    'Salario Bruto' as concepto, 75000.00 as monto
UNION ALL
SELECT 'AFP (2.87%)', afp_amount FROM calculate_dominican_deductions(75000.00)
UNION ALL
SELECT 'SFS (3.04%)', sfs_amount FROM calculate_dominican_deductions(75000.00)
UNION ALL
SELECT 'INFOTEP (1%)', infotep_amount FROM calculate_dominican_deductions(75000.00)
UNION ALL
SELECT 'ISR', isr_amount FROM calculate_dominican_deductions(75000.00)
UNION ALL
SELECT 'Total Deducciones', total_deductions FROM calculate_dominican_deductions(75000.00)
UNION ALL
SELECT 'Salario Neto', net_salary FROM calculate_dominican_deductions(75000.00);

-- Probar cálculo de ITBIS
SELECT 
    'Monto Base' as concepto, 1000.00 as monto
UNION ALL
SELECT 'ITBIS (18%)', calculate_itbis(1000.00, NULL, false)
UNION ALL
SELECT 'Total con ITBIS', 1000.00 + calculate_itbis(1000.00, NULL, false);

-- =====================================================
-- 3. DATOS DE LA EMPRESA DEMO
-- =====================================================

-- Información de la empresa demo
SELECT 
    u.full_name as "Usuario",
    u.email as "Email",
    c.name as "Empresa",
    c.rnc as "RNC",
    c.address as "Dirección"
FROM users u
JOIN companies c ON u.id = c.owner_id
WHERE u.email = '<EMAIL>';

-- Cuentas financieras
SELECT 
    name as "Cuenta",
    type as "Tipo",
    balance as "Balance",
    bank_name as "Banco",
    account_number as "Número"
FROM financial_accounts fa
JOIN companies c ON fa.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>';

-- =====================================================
-- 4. TRANSACCIONES FINANCIERAS
-- =====================================================

-- Resumen de transacciones por tipo
SELECT 
    ft.type as "Tipo",
    COUNT(*) as "Cantidad",
    SUM(ft.amount) as "Total",
    AVG(ft.amount) as "Promedio"
FROM financial_transactions ft
JOIN companies c ON ft.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
GROUP BY ft.type
ORDER BY "Total" DESC;

-- Transacciones recientes
SELECT 
    ft.transaction_date as "Fecha",
    ft.type as "Tipo",
    tc.name as "Categoría",
    ft.description as "Descripción",
    ft.amount as "Monto",
    fa.name as "Cuenta"
FROM financial_transactions ft
JOIN transaction_categories tc ON ft.category_id = tc.id
JOIN financial_accounts fa ON ft.account_id = fa.id
JOIN companies c ON ft.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY ft.transaction_date DESC, ft.created_at DESC
LIMIT 10;

-- =====================================================
-- 5. ANÁLISIS FINANCIERO
-- =====================================================

-- Balance por categoría
SELECT 
    tc.name as "Categoría",
    tc.type as "Tipo",
    COUNT(ft.id) as "Transacciones",
    SUM(ft.amount) as "Total"
FROM transaction_categories tc
LEFT JOIN financial_transactions ft ON tc.id = ft.category_id
JOIN companies c ON tc.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
GROUP BY tc.name, tc.type
ORDER BY tc.type, "Total" DESC;

-- Estado de resultados simplificado
WITH ingresos AS (
    SELECT COALESCE(SUM(amount), 0) as total
    FROM financial_transactions ft
    JOIN companies c ON ft.company_id = c.id
    JOIN users u ON c.owner_id = u.id
    WHERE u.email = '<EMAIL>' AND ft.type = 'ingreso'
),
gastos AS (
    SELECT COALESCE(SUM(amount), 0) as total
    FROM financial_transactions ft
    JOIN companies c ON ft.company_id = c.id
    JOIN users u ON c.owner_id = u.id
    WHERE u.email = '<EMAIL>' AND ft.type = 'gasto'
)
SELECT 
    'Ingresos Totales' as concepto, i.total as monto
FROM ingresos i
UNION ALL
SELECT 'Gastos Totales', g.total FROM gastos g
UNION ALL
SELECT 'Utilidad Neta', (i.total - g.total) FROM ingresos i, gastos g;

-- =====================================================
-- 6. GESTIÓN DE EMPLEADOS
-- =====================================================

-- Lista de empleados
SELECT 
    e.employee_code as "Código",
    e.first_name || ' ' || e.last_name as "Nombre Completo",
    d.name as "Departamento",
    e.position as "Posición",
    e.base_salary as "Salario Base",
    e.hire_date as "Fecha Contratación",
    e.status as "Estado"
FROM employees e
LEFT JOIN departments d ON e.department_id = d.id
JOIN companies c ON e.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY e.hire_date;

-- Cálculo de nómina para empleados
SELECT 
    e.first_name || ' ' || e.last_name as "Empleado",
    e.base_salary as "Salario Base",
    dd.afp_amount as "AFP",
    dd.sfs_amount as "SFS",
    dd.infotep_amount as "INFOTEP",
    dd.isr_amount as "ISR",
    dd.total_deductions as "Total Deducciones",
    dd.net_salary as "Salario Neto"
FROM employees e
JOIN companies c ON e.company_id = c.id
JOIN users u ON c.owner_id = u.id
CROSS JOIN LATERAL calculate_dominican_deductions(e.base_salary, c.id) dd
WHERE u.email = '<EMAIL>' AND e.status = 'active';

-- =====================================================
-- 7. PROVEEDORES Y CLIENTES
-- =====================================================

-- Proveedores
SELECT 
    name as "Proveedor",
    contact_name as "Contacto",
    email as "Email",
    phone as "Teléfono",
    payment_terms as "Términos Pago (días)",
    current_balance as "Balance Actual"
FROM suppliers s
JOIN companies c ON s.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY s.name;

-- Clientes
SELECT 
    name as "Cliente",
    contact_name as "Contacto",
    email as "Email",
    phone as "Teléfono",
    credit_limit as "Límite Crédito",
    current_balance as "Balance Actual"
FROM customers cu
JOIN companies c ON cu.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY cu.name;

-- =====================================================
-- 8. OBLIGACIONES FISCALES
-- =====================================================

-- Configuración de impuestos
SELECT 
    tax_type as "Tipo Impuesto",
    (rate * 100) || '%' as "Tasa",
    description as "Descripción",
    effective_from as "Vigente Desde"
FROM tax_configurations tc
JOIN companies c ON tc.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>' AND tc.is_active = true
ORDER BY tc.tax_type;

-- Obligaciones fiscales pendientes
SELECT 
    name as "Obligación",
    tax_type as "Tipo",
    due_date as "Fecha Vencimiento",
    amount as "Monto",
    status as "Estado",
    CASE 
        WHEN due_date < CURRENT_DATE THEN 'VENCIDA'
        WHEN due_date <= CURRENT_DATE + INTERVAL '7 days' THEN 'PRÓXIMA A VENCER'
        ELSE 'VIGENTE'
    END as "Situación"
FROM tax_obligations to_
JOIN companies c ON to_.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY to_.due_date;

-- =====================================================
-- 9. AUDITORÍA Y SEGURIDAD
-- =====================================================

-- Últimas actividades registradas
SELECT 
    al.created_at as "Fecha/Hora",
    al.table_name as "Tabla",
    al.action as "Acción",
    u.full_name as "Usuario"
FROM audit_logs al
JOIN companies c ON al.company_id = c.id
LEFT JOIN users u ON al.user_id = u.id
JOIN users owner ON c.owner_id = owner.id
WHERE owner.email = '<EMAIL>'
ORDER BY al.created_at DESC
LIMIT 10;

-- =====================================================
-- 10. REPORTES AVANZADOS
-- =====================================================

-- Balance general simplificado
SELECT * FROM get_balance_sheet(
    (SELECT c.id FROM companies c JOIN users u ON c.owner_id = u.id WHERE u.email = '<EMAIL>'),
    CURRENT_DATE
);

-- Flujo de efectivo por mes (últimos 6 meses)
SELECT 
    DATE_TRUNC('month', ft.transaction_date) as "Mes",
    SUM(CASE WHEN ft.type = 'ingreso' THEN ft.amount ELSE 0 END) as "Ingresos",
    SUM(CASE WHEN ft.type = 'gasto' THEN ft.amount ELSE 0 END) as "Gastos",
    SUM(CASE WHEN ft.type = 'ingreso' THEN ft.amount ELSE -ft.amount END) as "Flujo Neto"
FROM financial_transactions ft
JOIN companies c ON ft.company_id = c.id
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>'
AND ft.transaction_date >= CURRENT_DATE - INTERVAL '6 months'
GROUP BY DATE_TRUNC('month', ft.transaction_date)
ORDER BY "Mes" DESC;

-- =====================================================
-- MENSAJE FINAL
-- =====================================================

SELECT 
    '🎉 ¡Felicidades! La base de datos JARSS está funcionando perfectamente.' as mensaje
UNION ALL
SELECT '📊 Todas las funciones dominicanas están operativas.'
UNION ALL
SELECT '🇩🇴 Sistema listo para empresas dominicanas.'
UNION ALL
SELECT '💼 ¡Comienza a gestionar tus finanzas con JARSS!';
