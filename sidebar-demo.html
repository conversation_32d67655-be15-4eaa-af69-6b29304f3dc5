<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARSS - Barra Lateral Rediseñada</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        
        /* Scrollbar personalizado */
        .scrollbar-thin::-webkit-scrollbar { width: 6px; }
        .scrollbar-thin::-webkit-scrollbar-track { background: transparent; }
        .scrollbar-thin::-webkit-scrollbar-thumb { 
            background: rgb(203 213 225); 
            border-radius: 0.5rem; 
        }
        .dark .scrollbar-thin::-webkit-scrollbar-thumb { 
            background: rgb(71 85 105); 
        }
        
        /* Animaciones personalizadas */
        @keyframes slide-in-from-top-2 {
            from { transform: translateY(-8px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes slide-in-from-bottom-2 {
            from { transform: translateY(8px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .animate-slide-in-top { animation: slide-in-from-top-2 200ms ease-out; }
        .animate-slide-in-bottom { animation: slide-in-from-bottom-2 200ms ease-out; }
        
        /* Efectos de hover personalizados */
        .hover-scale:hover { transform: scale(1.05); }
        .hover-translate:hover { transform: translateX(4px); }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Toggle Button (Mobile) -->
    <button id="toggleBtn" class="fixed top-6 left-6 z-50 lg:hidden rounded-xl bg-white/95 backdrop-blur-xl shadow-xl border border-slate-200/50 dark:bg-slate-900/95 dark:border-slate-700/50 hover:bg-white dark:hover:bg-slate-800 transition-all duration-300 hover:scale-105 hover:shadow-2xl p-3">
        <svg id="menuIcon" class="w-5 h-5 text-slate-700 dark:text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
        <svg id="closeIcon" class="w-5 h-5 text-slate-700 dark:text-slate-300 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    </button>

    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-slate-200/50 dark:border-gray-700/50 transition-all duration-300 z-40 shadow-2xl shadow-slate-900/10 w-20 hover:w-72">
        <!-- Gradient overlay -->
        <div class="absolute inset-0 bg-gradient-to-b from-slate-50/50 via-transparent to-slate-100/30 dark:from-gray-800/50 dark:via-transparent dark:to-gray-900/30 pointer-events-none"></div>
        
        <div class="relative flex flex-col h-full">
            <!-- Header -->
            <div class="relative p-6 border-b border-slate-200/20 dark:border-slate-700/30 transition-all duration-300 h-20 flex items-center bg-gradient-to-r from-slate-50/50 to-blue-50/30 dark:from-slate-900/50 dark:to-blue-900/20">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 dark:from-blue-400/5 dark:to-indigo-400/5"></div>
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>
                
                <div class="relative flex items-center w-full">
                    <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg shadow-blue-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-105">
                        <div class="flex items-center justify-center w-full h-full rounded-xl bg-white/10 backdrop-blur-sm">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="ml-4 transition-all duration-300 opacity-0 sidebar-expanded-content">
                        <div class="flex items-center gap-2">
                            <h1 class="text-xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 dark:from-white dark:to-slate-200 bg-clip-text text-transparent">
                                JARSS
                            </h1>
                            <div class="px-2 py-0.5 text-xs font-medium text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30 rounded-full">
                                Pro
                            </div>
                        </div>
                        <p class="text-sm text-slate-500 dark:text-slate-400 font-medium">
                            Sistema Contable
                        </p>
                    </div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="px-4 py-3 border-b border-slate-200/20 dark:border-slate-700/30">
                <div class="relative transition-all duration-300">
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-400 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input
                            type="text"
                            placeholder="Buscar..."
                            class="w-full h-10 pl-10 pr-4 text-sm text-slate-700 dark:text-slate-300 placeholder-slate-400 bg-slate-100/50 dark:bg-slate-800/50 border border-slate-200/50 dark:border-slate-700/50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                        />
                    </div>
                </div>
            </div>

            <!-- Navigation Section -->
            <nav class="flex-1 px-3 py-4 space-y-1 overflow-y-auto scrollbar-thin">
                <!-- Main Section Label -->
                <div class="px-3 py-2 mb-4 sidebar-expanded-content opacity-0">
                    <h3 class="text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                        Principal
                    </h3>
                </div>
                
                <!-- Navigation Links -->
                <div class="mb-1">
                    <a href="#" class="relative flex items-center px-3 py-3 rounded-xl group transition-all duration-300 overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25 scale-[1.02] mx-2">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 animate-pulse"></div>
                        <div class="relative flex items-center justify-center w-8 h-8 rounded-lg bg-white/20 backdrop-blur-sm">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="relative ml-3 flex-1 sidebar-expanded-content opacity-0">
                            <span class="text-sm font-medium text-white">Panel</span>
                            <div class="absolute bottom-0 left-0 h-0.5 bg-current w-full opacity-100"></div>
                        </div>
                    </a>
                </div>

                <!-- Submenu Example -->
                <div class="mb-1">
                    <button class="relative flex items-center justify-between w-full px-3 py-3 rounded-xl group transition-all duration-300 overflow-hidden text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-100 mx-2">
                        <div class="relative flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-lg group-hover:bg-slate-200 dark:group-hover:bg-slate-700 transition-all duration-300">
                                <svg class="h-5 w-5 text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            </div>
                            <span class="ml-3 text-sm font-medium group-hover:translate-x-1 transition-all duration-300 sidebar-expanded-content opacity-0">
                                Gestión Financiera
                            </span>
                        </div>
                        <div class="relative sidebar-expanded-content opacity-0">
                            <svg class="w-4 h-4 text-slate-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                </div>

                <!-- More Navigation Items -->
                <div class="mb-1">
                    <a href="#" class="relative flex items-center px-3 py-3 rounded-xl group transition-all duration-300 overflow-hidden text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-100 mx-2">
                        <div class="flex items-center justify-center w-8 h-8 rounded-lg group-hover:bg-slate-200 dark:group-hover:bg-slate-700 transition-all duration-300">
                            <svg class="h-5 w-5 text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="relative ml-3 flex-1 sidebar-expanded-content opacity-0">
                            <span class="text-sm font-medium group-hover:translate-x-1 transition-all duration-300">Reportes</span>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </div>

                <div class="mb-1">
                    <a href="#" class="relative flex items-center px-3 py-3 rounded-xl group transition-all duration-300 overflow-hidden text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-100 mx-2">
                        <div class="flex items-center justify-center w-8 h-8 rounded-lg group-hover:bg-slate-200 dark:group-hover:bg-slate-700 transition-all duration-300">
                            <svg class="h-5 w-5 text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div class="relative ml-3 flex-1 sidebar-expanded-content opacity-0">
                            <span class="text-sm font-medium group-hover:translate-x-1 transition-all duration-300">Configuración</span>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                </div>
            </nav>

            <!-- Profile Section -->
            <div class="relative">
                <div class="p-4 border-t border-slate-200/20 dark:border-slate-700/30 bg-gradient-to-r from-slate-50/50 to-blue-50/30 dark:from-slate-900/50 dark:to-blue-900/20 transition-all duration-300 cursor-pointer hover:bg-slate-100/50 dark:hover:bg-slate-800/50">
                    <div class="flex items-center justify-center">
                        <div class="flex items-center gap-3">
                            <div class="relative">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/30">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-slate-800 rounded-full"></div>
                            </div>
                            
                            <div class="flex-1 min-w-0 sidebar-expanded-content opacity-0">
                                <p class="text-sm font-semibold text-slate-800 dark:text-slate-200 truncate">
                                    Joe Doe
                                </p>
                                <p class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-20 transition-all duration-300 p-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-4">
                JARSS - Barra Lateral Rediseñada
            </h1>
            <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-slate-200 dark:border-slate-700">
                <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-4">
                    Características del Nuevo Diseño
                </h2>
                <ul class="space-y-3 text-slate-600 dark:text-slate-400">
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Diseño moderno con efectos de vidrio y gradientes</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Animaciones suaves y micro-interacciones</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Campo de búsqueda integrado y funcional</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Perfil de usuario con menú desplegable</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Hover para expandir automáticamente</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Tooltips informativos en modo colapsado</span>
                    </li>
                    <li class="flex items-center gap-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Soporte completo para modo oscuro</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // JavaScript para funcionalidad de la sidebar
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('toggleBtn');
        const menuIcon = document.getElementById('menuIcon');
        const closeIcon = document.getElementById('closeIcon');
        let isExpanded = false;
        let hoverTimeout;

        // Toggle para móviles
        toggleBtn.addEventListener('click', () => {
            isExpanded = !isExpanded;
            updateSidebar();
        });

        // Hover para desktop
        sidebar.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            if (window.innerWidth >= 1024) { // lg breakpoint
                expandSidebar();
            }
        });

        sidebar.addEventListener('mouseleave', () => {
            if (window.innerWidth >= 1024) { // lg breakpoint
                hoverTimeout = setTimeout(() => {
                    collapseSidebar();
                }, 300);
            }
        });

        function expandSidebar() {
            sidebar.classList.remove('w-20');
            sidebar.classList.add('w-72');
            
            // Mostrar contenido expandido
            const expandedContent = document.querySelectorAll('.sidebar-expanded-content');
            expandedContent.forEach(el => {
                el.classList.remove('opacity-0');
                el.classList.add('opacity-100');
            });
        }

        function collapseSidebar() {
            sidebar.classList.remove('w-72');
            sidebar.classList.add('w-20');
            
            // Ocultar contenido expandido
            const expandedContent = document.querySelectorAll('.sidebar-expanded-content');
            expandedContent.forEach(el => {
                el.classList.remove('opacity-100');
                el.classList.add('opacity-0');
            });
        }

        function updateSidebar() {
            if (isExpanded) {
                expandSidebar();
                menuIcon.classList.add('hidden');
                closeIcon.classList.remove('hidden');
            } else {
                collapseSidebar();
                menuIcon.classList.remove('hidden');
                closeIcon.classList.add('hidden');
            }
        }

        // Inicializar estado
        collapseSidebar();
    </script>
</body>
</html>
