generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(uuid())
  email         String    @unique
  name          String
  password      String
  role          Role      @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  transactions  Transaction[]
  budgets       Budget[]
}

model Transaction {
  id          String    @id @default(uuid())
  amount      Float
  type        TransactionType
  description String
  date        DateTime
  category    String
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Budget {
  id          String    @id @default(uuid())
  amount      Float
  category    String
  period      String    // Mensual, Trimestral, Anual
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

enum Role {
  USER
  ADMIN
  FINANCE_MANAGER
}

enum TransactionType {
  INCOME
  EXPENSE
} 