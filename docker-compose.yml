version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: jarss_db
    environment:
      POSTGRES_USER: jarss_user
      POSTGRES_PASSWORD: jarss_password
      POSTGRES_DB: jarss_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - jarss_network

  pgadmin:
    image: dpage/pgadmin4
    container_name: jarss_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - jarss_network

volumes:
  postgres_data:

networks:
  jarss_network:
    driver: bridge