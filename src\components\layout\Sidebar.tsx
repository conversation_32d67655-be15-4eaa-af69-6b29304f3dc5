import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { JarssLogo } from "@/components/icons/JarssLogo";

const linksMain = [
  { name: "Dashboard", href: "/" },
  { name: "Reportes", href: "/reportes" },
];
const linksSettings = [
  { name: "Ajuste<PERSON>", href: "/configuracion" },
];

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  return (
    <nav
      className={cn(
        "fixed left-0 top-0 h-full z-40 bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] border-r border-[hsl(var(--sidebar-border))] transition-all duration-300",
        collapsed ? "w-20" : "w-64"
      )}
    >
      {/* Header/logo */}
      <div className="flex items-center gap-2 h-16 px-4 border-b border-[hsl(var(--sidebar-border))]">
        <JarssLogo size={40} />
        {!collapsed && <span className="font-bold text-xl">JARSS</span>}
        <button
          className="ml-auto p-2 rounded hover:bg-[hsl(var(--sidebar-accent))]/20 transition"
          onClick={() => setCollapsed((v) => !v)}
          aria-label="Expandir/Colapsar"
        >
          <span className="material-icons text-lg">{collapsed ? "chevron_right" : "chevron_left"}</span>
        </button>
      </div>
      {/* Buscador */}
      <div className="px-4 py-2 border-b border-[hsl(var(--sidebar-border))]">
        <input
          type="search"
          placeholder="Buscar..."
          className="w-full px-3 py-2 rounded bg-[hsl(var(--background))] text-[hsl(var(--foreground))] outline-none"
          onFocus={() => setCollapsed(false)}
        />
      </div>
      {/* Enlaces principales */}
      <div className="px-2 py-4">
        <h2 className={cn("text-xs font-semibold uppercase mb-2", collapsed && "hidden")}>Principal</h2>
        <ul className="space-y-1">
          {linksMain.map((link) => (
            <li key={link.name}>
              <Link
                to={link.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded hover:bg-[hsl(var(--sidebar-accent))]/20 transition",
                  location.pathname === link.href && "bg-[hsl(var(--sidebar-accent))]/30 font-bold"
                )}
                title={link.name}
              >
                <span className="material-icons">dashboard</span>
                {!collapsed && <span>{link.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      {/* Enlaces de configuración */}
      <div className="px-2 mt-auto pb-4">
        <h2 className={cn("text-xs font-semibold uppercase mb-2", collapsed && "hidden")}>Configuración</h2>
        <ul className="space-y-1">
          {linksSettings.map((link) => (
            <li key={link.name}>
              <Link
                to={link.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded hover:bg-[hsl(var(--sidebar-accent))]/20 transition",
                  location.pathname === link.href && "bg-[hsl(var(--sidebar-accent))]/30 font-bold"
                )}
                title={link.name}
              >
                <span className="material-icons">settings</span>
                {!collapsed && <span>{link.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      {/* Perfil de usuario */}
      <div className={cn("flex items-center gap-3 px-4 py-4 border-t border-[hsl(var(--sidebar-border))]", collapsed && "justify-center")}> 
        <img src="/assets/profile.png" alt="Usuario" className="w-10 h-10 rounded-full" />
        {!collapsed && (
          <div>
            <div className="font-semibold">Usuario JARSS</div>
            <div className="text-xs text-[hsl(var(--sidebar-foreground))]/70"><EMAIL></div>
          </div>
        )}
      </div>
    </nav>
  );
}
