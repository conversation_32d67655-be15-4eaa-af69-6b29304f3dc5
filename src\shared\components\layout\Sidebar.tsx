import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  Wallet,
  FileText,
  PieChart,
  Users,
  BarChartHorizontal,
  CreditCard,
  Settings,
  Search,
  Command
} from "lucide-react";

import { SidebarLink } from "./sidebar/SidebarLink";
import { SidebarSubmenu } from "./sidebar/SidebarSubmenu";
import { SidebarProfile } from "./sidebar/SidebarProfile";
import { SidebarHeader } from "./sidebar/SidebarHeader";
import { SidebarToggle } from "./sidebar/SidebarToggle";

type SidebarLinkType = {
  name: string;
  icon: React.ComponentType<any>;
  href: string;
  submenu?: { name: string; href: string }[];
};

const sidebarLinks: SidebarLinkType[] = [
  { name: "Panel", icon: BarChart3, href: "/" },
  { name: "Gestión Financiera", icon: Wallet, href: "/finanzas",
    submenu: [
      { name: "Ingresos", href: "/finanzas/ingresos" },
      { name: "Gas<PERSON>", href: "/finanzas/gastos" },
      { name: "Inversiones", href: "/finanzas/inversiones" }
    ]
  },
  { name: "Reportes", icon: FileText, href: "/reportes" },
  { name: "Cumplimiento Fiscal", icon: PieChart, href: "/impuestos" },
  { name: "Control Presupuestario", icon: BarChartHorizontal, href: "/presupuestos" },
  { name: "Nómina", icon: Users, href: "/nomina" },
  { name: "Bancos y Proveedores", icon: CreditCard, href: "/bank-accounts" },
  { name: "Configuración", icon: Settings, href: "/configuracion" },
];

export function Sidebar() {
  const [isOpen, setIsOpen] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const [isIconOnly, setIsIconOnly] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const location = useLocation();

  const toggleSubmenu = (name: string) => {
    setOpenSubmenu(openSubmenu === name ? null : name);
  };

  // Set active submenu based on current route
  useEffect(() => {
    const currentLink = sidebarLinks.find(link =>
      link.submenu?.some(subItem => location.pathname === subItem.href)
    );

    if (currentLink) {
      setOpenSubmenu(currentLink.name);
    }
  }, [location.pathname]);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
    setIsIconOnly(!isIconOnly);
  };

  const handleMouseEnter = () => {
    setIsOpen(true);
    setIsIconOnly(false);
  };

  const handleMouseLeave = () => {
    setIsOpen(false);
    setIsIconOnly(true);
  };

  const handleSearchFocus = () => {
    if (isIconOnly) {
      setIsOpen(true);
      setIsIconOnly(false);
    }
  };

  return (
    <>
      <SidebarToggle isOpen={isOpen} toggleSidebar={toggleSidebar} />

      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white dark:bg-gray-900 border-r border-slate-200 dark:border-gray-700 transition-all duration-300 z-40 shadow-lg",
          isOpen ? "w-72" : "w-20"
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-50 via-transparent to-slate-100 dark:from-gray-800 dark:via-transparent dark:to-gray-900 pointer-events-none opacity-30" />

        <div className="relative flex flex-col h-full">
          <SidebarHeader isIconOnly={isIconOnly} />

          {/* Search Section */}
          <div className="px-4 py-3 border-b border-slate-200 dark:border-gray-600">
            <div className={cn(
              "relative transition-all duration-300",
              isIconOnly ? "w-12 mx-auto" : "w-full"
            )}>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 dark:text-gray-400" />
                <input
                  type="text"
                  placeholder={isIconOnly ? "" : "Buscar..."}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={handleSearchFocus}
                  className={cn(
                    "w-full bg-slate-100 dark:bg-gray-800 border border-slate-200 dark:border-gray-600 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                    isIconOnly
                      ? "h-12 pl-4 pr-4"
                      : "h-10 pl-10 pr-4 text-sm text-slate-700 dark:text-gray-100 placeholder-slate-400 dark:placeholder-gray-400"
                  )}
                />
                {!isIconOnly && searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Navigation Section */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-slate-600 scrollbar-track-transparent">
            {/* Main Section Label */}
            {!isIconOnly && (
              <div className="px-3 py-2 mb-4">
                <h3 className="text-xs font-semibold text-slate-500 dark:text-gray-300 uppercase tracking-wider">
                  Principal
                </h3>
              </div>
            )}

            {sidebarLinks.map((link) => (
              <div key={link.name} className="mb-1">
                {link.submenu ? (
                  <SidebarSubmenu
                    name={link.name}
                    icon={link.icon}
                    href={link.href}
                    submenu={link.submenu}
                    isOpen={openSubmenu === link.name}
                    isIconOnly={isIconOnly}
                    currentPath={location.pathname}
                    onToggle={toggleSubmenu}
                  />
                ) : (
                  <SidebarLink
                    name={link.name}
                    icon={link.icon}
                    href={link.href}
                    isActive={location.pathname === link.href}
                    isIconOnly={isIconOnly}
                  />
                )}
              </div>
            ))}
          </nav>

          <SidebarProfile isIconOnly={isIconOnly} />
        </div>
      </div>
    </>
  );
}
