@echo off
REM JARSS - Configuración para Windows
REM Sistema Integral de Gestión Financiera

echo.
echo   ╔══════════════════════════════════════════════════════════════╗
echo   ║                            JARSS                             ║
echo   ║              Sistema Integral de Gestión Financiera          ║
echo   ║                    Configuración Windows                     ║
echo   ╚══════════════════════════════════════════════════════════════╝
echo.

REM Verificar si PostgreSQL está instalado
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL no está instalado.
    echo.
    echo 📥 OPCIONES DE INSTALACIÓN:
    echo.
    echo 1. INSTALACIÓN AUTOMÁTICA CON DOCKER ^(Recomendada^):
    echo    • Instala Docker Desktop desde: https://www.docker.com/products/docker-desktop
    echo    • Ejecuta: docker-setup.bat
    echo.
    echo 2. INSTALACIÓN MANUAL DE POSTGRESQL:
    echo    • Descarga PostgreSQL desde: https://www.postgresql.org/download/windows/
    echo    • Instala PostgreSQL 15 o superior
    echo    • Ejecuta este script nuevamente
    echo.
    echo 3. USAR POSTGRESQL ONLINE:
    echo    • Crea una cuenta en https://www.elephantsql.com/ ^(gratis^)
    echo    • O usa https://supabase.com/ ^(gratis^)
    echo    • Configura manualmente las credenciales
    echo.
    pause
    exit /b 1
)

echo ✅ PostgreSQL encontrado

REM Variables de configuración por defecto
set DB_NAME=jarss
set DB_USER=jarss_user
set DB_HOST=localhost
set DB_PORT=5432

echo.
echo 📊 Configuración de la base de datos:
set /p DB_NAME="Nombre de la base de datos [%DB_NAME%]: " || set DB_NAME=%DB_NAME%
set /p DB_USER="Usuario de la base de datos [%DB_USER%]: " || set DB_USER=%DB_USER%
set /p DB_PASSWORD="Contraseña para %DB_USER%: "

if "%DB_PASSWORD%"=="" (
    echo ❌ La contraseña no puede estar vacía
    pause
    exit /b 1
)

set /p PG_ADMIN_USER="Usuario administrador de PostgreSQL [postgres]: " || set PG_ADMIN_USER=postgres

echo.
echo 🔄 Creando base de datos y usuario...

REM Crear usuario
psql -h %DB_HOST% -p %DB_PORT% -U %PG_ADMIN_USER% -c "CREATE USER %DB_USER% WITH PASSWORD '%DB_PASSWORD%';" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  El usuario %DB_USER% ya existe o hubo un error
)

REM Crear base de datos
psql -h %DB_HOST% -p %DB_PORT% -U %PG_ADMIN_USER% -c "CREATE DATABASE %DB_NAME% OWNER %DB_USER%;" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  La base de datos %DB_NAME% ya existe o hubo un error
)

REM Otorgar permisos
psql -h %DB_HOST% -p %DB_PORT% -U %PG_ADMIN_USER% -c "GRANT ALL PRIVILEGES ON DATABASE %DB_NAME% TO %DB_USER%;"

echo ✅ Base de datos y usuario configurados

REM Verificar archivos SQL
if not exist "install.sql" (
    echo ❌ Archivo install.sql no encontrado. Ejecuta este script desde el directorio database\
    pause
    exit /b 1
)

echo 📦 Instalando esquema de JARSS...

REM Configurar variable de entorno para la contraseña
set PGPASSWORD=%DB_PASSWORD%

REM Ejecutar instalación
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f install.sql

if %errorlevel% neq 0 (
    echo ❌ Error durante la instalación
    pause
    exit /b 1
)

echo ✅ JARSS instalado exitosamente

REM Crear archivo .env
echo 📝 Creando archivo de configuración...
(
echo # JARSS - Sistema Integral de Gestión Financiera
echo # Configuración generada automáticamente
echo # Fecha: %date% %time%
echo.
echo # Database Configuration ^(PostgreSQL^)
echo DATABASE_HOST=%DB_HOST%
echo DATABASE_PORT=%DB_PORT%
echo DATABASE_NAME=%DB_NAME%
echo DATABASE_USER=%DB_USER%
echo DATABASE_PASSWORD=%DB_PASSWORD%
echo DATABASE_SSL=false
echo.
echo # Application Configuration
echo VITE_APP_NAME=JARSS
echo VITE_APP_VERSION=1.0.0
echo VITE_APP_ENVIRONMENT=development
echo.
echo # API Configuration
echo VITE_API_URL=http://localhost:3000/api
echo API_PORT=3000
echo.
echo # Authentication ^& Security
echo JWT_SECRET=jarss_jwt_secret_%random%
echo JWT_EXPIRES_IN=24h
echo SESSION_TIMEOUT=3600
echo MAX_LOGIN_ATTEMPTS=5
echo BCRYPT_ROUNDS=12
echo.
echo # Dominican Republic Tax Configuration
echo VITE_ITBIS_RATE=0.18
echo VITE_AFP_RATE=0.0287
echo VITE_SFS_RATE=0.0304
echo VITE_INFOTEP_RATE=0.01
echo.
echo # Feature Flags
echo VITE_ENABLE_DEMO_DATA=true
) > ..\.env

echo ✅ Archivo .env creado

REM Crear datos de demostración
echo.
set /p create_demo="¿Deseas crear datos de demostración? (Y/N): "
if /i "%create_demo%"=="Y" (
    echo 🎭 Creando datos de demostración...
    
    psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO users (email, password_hash, full_name) VALUES ('<EMAIL>', hash_password('demo123'), 'Usuario Demo');"
    
    psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO companies (owner_id, name, rnc, address) SELECT id, 'Empresa Demo JARSS', '101-12345-6', 'Santo Domingo, República Dominicana' FROM users WHERE email = '<EMAIL>';"
    
    psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT setup_new_company(c.id) FROM companies c JOIN users u ON c.owner_id = u.id WHERE u.email = '<EMAIL>';"
    
    psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT create_demo_data(c.id) FROM companies c JOIN users u ON c.owner_id = u.id WHERE u.email = '<EMAIL>';"
    
    echo ✅ Datos de demostración creados
    echo.
    echo 🔑 Credenciales de demostración:
    echo   Email: <EMAIL>
    echo   Contraseña: demo123
)

REM Limpiar variable de entorno
set PGPASSWORD=

REM Resumen final
echo.
echo 🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE! 🎉
echo.
echo 📋 RESUMEN DE CONFIGURACIÓN:
echo   • Base de datos: %DB_NAME%
echo   • Usuario: %DB_USER%
echo   • Host: %DB_HOST%:%DB_PORT%
echo   • Archivo de configuración: .env
echo.
echo 🚀 COMANDOS ÚTILES:
echo   • Conectar a la DB: psql -h %DB_HOST% -U %DB_USER% -d %DB_NAME%
echo   • Backup: pg_dump -h %DB_HOST% -U %DB_USER% %DB_NAME% ^> backup.sql
echo   • Restore: psql -h %DB_HOST% -U %DB_USER% -d %DB_NAME% ^< backup.sql
echo.
echo 📚 DOCUMENTACIÓN:
echo   • README.md - Información general
echo   • database\README.md - Documentación de la base de datos
echo.
echo ⚠️  IMPORTANTE:
echo   • Cambia las contraseñas por defecto en producción
echo   • Configura HTTPS para producción
echo   • Realiza respaldos regulares de la base de datos
echo.
echo ✅ JARSS está listo para usar!
echo.
pause
